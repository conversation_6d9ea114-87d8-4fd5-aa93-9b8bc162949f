<!DOCTYPE html>
<html lang="en-GB">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Contact Us - SailorPay Improved</title>

    <!-- favicons-->
    <link rel="icon" type="image/svg+xml" href="images/logos/favicon.svg">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="manifest" href="site.webmanifest">
    <link rel="mask-icon" href="safari-pinned-tab.svg" color="#226191">
    <meta name="msapplication-TileColor" content="#226191">
    <meta name="theme-color" content="#ffffff">

    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Archivo+Black&display=swap">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Main CSS -->
    <link rel="stylesheet" href="css/variables.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/style.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/fixes.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/footer.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/navbar.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/common.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/modern.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/animations.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/icons.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/payment-logos.css" type="text/css" media="all">
    <link rel="stylesheet" href="css/hero-banner.css" type="text/css" media="all">

    <style>
        .contact-form {
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 5px;
            margin-top: 20px;
        }

        .contact-form input[type="text"],
        .contact-form input[type="email"],
        .contact-form input[type="tel"],
        .contact-form input[type="url"],
        .contact-form textarea,
        .contact-form select {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .contact-form button {
            background-color: #226191;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }

        .contact-form button:hover {
            background-color: #184a73;
        }

        .contact-info {
            margin-top: 40px;
        }

        .contact-info h3 {
            margin-bottom: 15px;
        }

        .contact-info p {
            margin-bottom: 10px;
        }

        /* Office locations styles */
        .office-locations {
            margin-top: 40px;
        }

        .office-card {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .office-card h3 {
            color: #226191;
            margin-bottom: 20px;
        }

        .map-container {
            width: 100%;
            height: 250px;
            border-radius: 8px;
            overflow: hidden;
            margin-top: 15px;
        }

        .map-container iframe {
            width: 100%;
            height: 100%;
            border: 0;
        }

        .contact-split {
            margin-top: 60px;
        }

        /* Social links styling */
        .telegram-link {
            color: #0088cc;
            text-decoration: none;
        }

        .telegram-link:hover {
            color: #006699;
        }

        .whatsapp-link {
            color: #25d366;
            text-decoration: none;
        }

        .whatsapp-link:hover {
            color: #1da851;
        }

        .linkedin-link {
            color: #0077b5;
            text-decoration: none;
        }

        .linkedin-link:hover {
            color: #005885;
        }

        .social-links a {
            margin-right: 15px;
            display: inline-block;
        }
    </style>
</head>

<body class="page-template-default page">
    <div id="page" class="site siteWrapper">
        <a class="skip-link screen-reader-text" href="#content">Skip to content</a>

        <a name="top"></a>

        <header id="masthead" class="site-header">
            <div class="wrapper header-wrapper">
                <div class="site-branding">
                    <a href="index.html">
                        <img src="images/logo.png" alt="SailorPay" width="150" height="50">
                    </a>
                </div>

                <div class="header-contact-info">
                    <button type="button" aria-label="Menu" aria-controls="navigation" class="hamburger hamburger--spin" id="menu-toggle">
                        <span class="hamburger-box">
                            <span class="hamburger-inner"></span>
                        </span>
                    </button>
                </div>

                <nav id="site-navigation" class="main-navigation">
                    <ul id="primary-menu" class="menu">
                        <li class="menu-item"><a href="index.html">Home</a></li>
                        <li class="menu-item"><a href="services.html">Services</a></li>
                        <li class="menu-item"><a href="company.html">Company</a></li>
                        <li class="menu-item current-menu-item"><a href="contact.html">Contact Us</a></li>
                    </ul>
                </nav>
            </div>

            <div class="mobile-menu" id="mobile-menu" style="left: -210px;">
                <ul class="menu">
                    <li class="menu-item"><a href="index.html">Home</a></li>
                    <li class="menu-item"><a href="services.html">Services</a></li>
                    <li class="menu-item"><a href="company.html">Company</a></li>
                    <li class="menu-item current-menu-item"><a href="contact.html">Contact Us</a></li>
                </ul>
            </div>

        </header><!-- #masthead -->

        <div id="content" class="site-content">
            <div id="primary" class="content-area">
                <main id="main" class="site-main">
                    <section id="contact-intro">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12">
                                    <h1>Contact Us</h1>
                                    <p class="blueText largeText">Get in touch with our team to learn more about our payment solutions and how we can help your business navigate the world of online payments.</p>
                                    <p>We're here to answer your questions and provide the support you need to succeed with our payment processing system.</p>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>

                    <!-- Contact Form Section -->
                    <section id="contact-form-section">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12">
                                    <h2>Get In Touch</h2>
                                    <p>Questions? Need Support? We're here to help you succeed with our payment processing solutions.</p>
                                </div>
                            </div><!--row-->

                            <div class="row">
                                <div class="col col--alignLeft col-6">
                                    <div class="contact-form">
                                        <h3>Send us a Message</h3>
                                        <form>
                                            <input type="text" placeholder="Your Name" required>
                                            <input type="email" placeholder="Email" required>
                                            <input type="text" placeholder="Company (Optional)">
                                            <textarea placeholder="How can we help you?" rows="5" required></textarea>
                                            <button type="submit">Submit</button>
                                        </form>
                                    </div>
                                </div>

                                <div class="col col--alignLeft col-6">
                                    <div class="contact-info">
                                        <h3>Contact Information</h3>
                                        <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                                        <p><strong>LinkedIn:</strong> <a href="https://www.linkedin.com/company/sailorpay" target="_blank" class="linkedin-link"><i class="fab fa-linkedin-in"></i> linkedin.com/company/sailorpay</a></p>
                                        <p><strong>WhatsApp:</strong> <a href="https://wa.me/************" target="_blank" class="whatsapp-link"><i class="fab fa-whatsapp"></i> +43 ************</a></p>
                                        <p><strong>Telegram:</strong> <a href="https://t.me/sailorpay" target="_blank" class="telegram-link"><i class="fab fa-telegram-plane"></i> @sailorpay</a></p>
                                        <p><strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM (CET)</p>

                                        <div class="social-contact-links">
                                            <p>Or reach us directly via:</p>
                                            <div class="social-links">
                                                <a href="https://www.linkedin.com/company/sailorpay" target="_blank" class="linkedin-link"><i class="fab fa-linkedin-in"></i> LinkedIn</a>
                                                <a href="https://wa.me/************" target="_blank" class="whatsapp-link"><i class="fab fa-whatsapp"></i> WhatsApp</a>
                                                <a href="https://t.me/sailorpay" target="_blank" class="telegram-link"><i class="fab fa-telegram-plane"></i> Telegram</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>

                    <!-- Office Locations Section -->
                    <section id="office-locations" class="office-locations">
                        <div class="wrapper">
                            <div class="row">
                                <div class="col col--alignCenter col-12">
                                    <h2>Our Office Locations</h2>
                                    <p>Visit us at our offices across Europe for personalized support and consultation.</p>
                                </div>
                            </div><!--row-->

                            <div class="row">
                                <!-- SailorPay SL - Marbella, Spain -->
                                <div class="col col-6">
                                    <div class="office-card">
                                        <h3>SailorPay SL</h3>
                                        <p><strong>Marbella Office</strong></p>
                                        <p>Avenida Ricardo Soriano, 29<br>
                                        29601 Marbella, Málaga<br>
                                        Spain</p>
                                        <p><strong>Phone:</strong> <a href="tel:+34952123456">+34 952 123 456</a></p>
                                        <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>

                                        <div class="map-container">
                                            <iframe
                                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3198.7************!2d-4.8856789!3d36.5108456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzbCsDMwJzM5LjAiTiA0wrA1Myc4LjQiVw!5e0!3m2!1sen!2ses!4v1************!5m2!1sen!2ses"
                                                allowfullscreen=""
                                                loading="lazy"
                                                referrerpolicy="no-referrer-when-downgrade"
                                                title="SailorPay SL Marbella Office Location">
                                            </iframe>
                                        </div>
                                    </div>
                                </div>

                                <!-- SailorPay EU - Vienna, Austria -->
                                <div class="col col-6">
                                    <div class="office-card">
                                        <h3>SailorPay EU</h3>
                                        <p><strong>Vienna Office</strong></p>
                                        <p>Kaltenleutgebnerstraße 24/11.1A<br>
                                        1230 Wien<br>
                                        Austria</p>
                                        <p><strong>Phone:</strong> <a href="tel:+************">+43 ************</a></p>
                                        <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                                        <p><strong>UID:</strong> ATU73505156</p>

                                        <div class="map-container">
                                            <iframe
                                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2661.************!2d16.2856789!3d48.1408456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x601d9b7c8b123456%3A0x789abcdef0123456!2sKaltenleutgebnerstra%C3%9Fe%2024%2C%201230%20Wien%2C%20Austria!5e0!3m2!1sen!2sat!4v1************!5m2!1sen!2sat"
                                                allowfullscreen=""
                                                loading="lazy"
                                                referrerpolicy="no-referrer-when-downgrade"
                                                title="SailorPay EU Vienna Office Location">
                                            </iframe>
                                        </div>
                                    </div>
                                </div>
                            </div><!--row-->
                        </div><!--wrapper-->
                    </section>
                </main><!-- #main -->
            </div><!-- #primary -->
        </div><!-- #content -->

        <!-- Footer will be loaded here -->
        <div id="footer-container"></div>
    </div><!-- #page -->

    <!-- JavaScript for menu functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Hamburger menu toggle
            const menuToggle = document.querySelector('.hamburger');
            const mobileMenu = document.getElementById('mobile-menu');

            if (menuToggle && mobileMenu) {
                menuToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    menuToggle.classList.toggle('is-active');

                    // Directly manipulate the style for better compatibility
                    if (mobileMenu.style.left === '-210px' || mobileMenu.style.left === '') {
                        mobileMenu.style.left = '0';
                        mobileMenu.style.opacity = '1';
                        mobileMenu.style.visibility = 'visible';
                        mobileMenu.classList.add('active');
                        document.body.classList.add('menu-open'); // Prevent body scrolling
                    } else {
                        mobileMenu.style.left = '-210px';
                        mobileMenu.style.opacity = '0';
                        mobileMenu.style.visibility = 'hidden';
                        mobileMenu.classList.remove('active');
                        document.body.classList.remove('menu-open'); // Re-enable body scrolling
                    }

                    console.log('Hamburger clicked, mobile menu left:', mobileMenu.style.left);
                });
            }

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                if (mobileMenu && mobileMenu.classList.contains('active') &&
                    !mobileMenu.contains(event.target) &&
                    !menuToggle.contains(event.target)) {
                    mobileMenu.style.left = '-210px';
                    mobileMenu.style.opacity = '0';
                    mobileMenu.style.visibility = 'hidden';
                    mobileMenu.classList.remove('active');
                    menuToggle.classList.remove('is-active');
                    document.body.classList.remove('menu-open'); // Re-enable body scrolling
                    console.log('Clicked outside, closing menu');
                }
            });
        });
    </script>
    <script src="js/footer-loader.js"></script>
</body>
</html>
